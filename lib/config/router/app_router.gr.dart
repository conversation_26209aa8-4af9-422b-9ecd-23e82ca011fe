// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i39;
import 'package:flutter/material.dart' as _i40;
import 'package:gotcha_mfg_app/features/auth/presentation/pages/login_page.dart'
    as _i13;
import 'package:gotcha_mfg_app/features/auth/presentation/pages/password_reset_page.dart'
    as _i7;
import 'package:gotcha_mfg_app/features/auth/presentation/pages/personalise_account_page.dart'
    as _i24;
import 'package:gotcha_mfg_app/features/auth/presentation/pages/sign_up_page.dart'
    as _i32;
import 'package:gotcha_mfg_app/features/exercise/data/models/new_exercise_response.dart'
    as _i42;
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart'
    as _i44;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/exercise_feedback_page.dart'
    as _i1;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/flip_text_new_page.dart'
    as _i6;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/image_text_new_page.dart'
    as _i12;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/mixed_exercise_page.dart'
    as _i14;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/reflection_complete_new.dart'
    as _i26;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/reflection_multi_new.dart'
    as _i27;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/reflection_text_new.dart'
    as _i28;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/text_animation_new_page.dart'
    as _i34;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/video_player_new_page.dart'
    as _i35;
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/workout_page.dart'
    as _i38;
import 'package:gotcha_mfg_app/features/explore/presentation/pages/explore_home_page.dart'
    as _i2;
import 'package:gotcha_mfg_app/features/favourites/presentation/pages/favourites_page.dart'
    as _i3;
import 'package:gotcha_mfg_app/features/feedback/data/models/add_feedback.dart'
    as _i41;
import 'package:gotcha_mfg_app/features/feedback/presentation/pages/feedback_page.dart'
    as _i4;
import 'package:gotcha_mfg_app/features/feedback/presentation/pages/feedback_result.dart'
    as _i5;
import 'package:gotcha_mfg_app/features/help_seeking/presentation/pages/help_seeking_pathway.dart'
    as _i10;
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart'
    as _i43;
import 'package:gotcha_mfg_app/features/home/<USER>/pages/home_page.dart'
    as _i11;
import 'package:gotcha_mfg_app/features/home/<USER>/pages/normal_feed_page.dart'
    as _i15;
import 'package:gotcha_mfg_app/features/home/<USER>/pages/welcome_feed_page.dart'
    as _i37;
import 'package:gotcha_mfg_app/features/notification/presentation/pages/notification_allow_page.dart'
    as _i16;
import 'package:gotcha_mfg_app/features/notification/presentation/pages/notification_permission_page.dart'
    as _i17;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_dealing_with_page.dart'
    as _i19;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_feeling_page.dart'
    as _i20;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_goal_page.dart'
    as _i21;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_mental_boosts_page.dart'
    as _i22;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_thanks_page.dart'
    as _i18;
import 'package:gotcha_mfg_app/features/onboarding/presentation/pages/onboarding_welcome_page.dart'
    as _i23;
import 'package:gotcha_mfg_app/features/profile/presentation/pages/gotcha_web_view_page.dart'
    as _i8;
import 'package:gotcha_mfg_app/features/profile/presentation/pages/gym_history_page.dart'
    as _i9;
import 'package:gotcha_mfg_app/features/profile/presentation/pages/profile_page.dart'
    as _i25;
import 'package:gotcha_mfg_app/features/quiz/data/models/assessment_response.dart'
    as _i45;
import 'package:gotcha_mfg_app/features/quiz/presentation/pages/self_assesment_welcome.dart'
    as _i30;
import 'package:gotcha_mfg_app/features/quiz/presentation/pages/self_assessment_result.dart'
    as _i29;
import 'package:gotcha_mfg_app/features/quiz/presentation/pages/self_assessment_start.dart'
    as _i31;
import 'package:gotcha_mfg_app/features/splash/presentation/pages/splash_page.dart'
    as _i33;
import 'package:gotcha_mfg_app/features/village/presentations/pages/village_home.dart'
    as _i36;

/// generated route for
/// [_i1.ExerciseFeedbackPage]
class ExerciseFeedbackRoute
    extends _i39.PageRouteInfo<ExerciseFeedbackRouteArgs> {
  ExerciseFeedbackRoute({
    _i40.Key? key,
    required bool feedback,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          ExerciseFeedbackRoute.name,
          args: ExerciseFeedbackRouteArgs(
            key: key,
            feedback: feedback,
          ),
          initialChildren: children,
        );

  static const String name = 'ExerciseFeedbackRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ExerciseFeedbackRouteArgs>();
      return _i1.ExerciseFeedbackPage(
        key: args.key,
        feedback: args.feedback,
      );
    },
  );
}

class ExerciseFeedbackRouteArgs {
  const ExerciseFeedbackRouteArgs({
    this.key,
    required this.feedback,
  });

  final _i40.Key? key;

  final bool feedback;

  @override
  String toString() {
    return 'ExerciseFeedbackRouteArgs{key: $key, feedback: $feedback}';
  }
}

/// generated route for
/// [_i2.ExplorePage]
class ExploreRoute extends _i39.PageRouteInfo<void> {
  const ExploreRoute({List<_i39.PageRouteInfo>? children})
      : super(
          ExploreRoute.name,
          initialChildren: children,
        );

  static const String name = 'ExploreRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i2.ExplorePage();
    },
  );
}

/// generated route for
/// [_i3.FavouritesPage]
class FavouritesRoute extends _i39.PageRouteInfo<void> {
  const FavouritesRoute({List<_i39.PageRouteInfo>? children})
      : super(
          FavouritesRoute.name,
          initialChildren: children,
        );

  static const String name = 'FavouritesRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i3.FavouritesPage();
    },
  );
}

/// generated route for
/// [_i4.FeedBackPage]
class FeedBackRoute extends _i39.PageRouteInfo<void> {
  const FeedBackRoute({List<_i39.PageRouteInfo>? children})
      : super(
          FeedBackRoute.name,
          initialChildren: children,
        );

  static const String name = 'FeedBackRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i4.FeedBackPage();
    },
  );
}

/// generated route for
/// [_i5.FeedBackResultsPage]
class FeedBackResultsRoute
    extends _i39.PageRouteInfo<FeedBackResultsRouteArgs> {
  FeedBackResultsRoute({
    _i40.Key? key,
    required _i41.AddFeedbackData feedback,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          FeedBackResultsRoute.name,
          args: FeedBackResultsRouteArgs(
            key: key,
            feedback: feedback,
          ),
          initialChildren: children,
        );

  static const String name = 'FeedBackResultsRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FeedBackResultsRouteArgs>();
      return _i5.FeedBackResultsPage(
        key: args.key,
        feedback: args.feedback,
      );
    },
  );
}

class FeedBackResultsRouteArgs {
  const FeedBackResultsRouteArgs({
    this.key,
    required this.feedback,
  });

  final _i40.Key? key;

  final _i41.AddFeedbackData feedback;

  @override
  String toString() {
    return 'FeedBackResultsRouteArgs{key: $key, feedback: $feedback}';
  }
}

/// generated route for
/// [_i6.FlipTextNewPage]
class FlipTextNewRoute extends _i39.PageRouteInfo<FlipTextNewRouteArgs> {
  FlipTextNewRoute({
    _i40.Key? key,
    required _i42.Media media,
    required _i40.VoidCallback onComplete,
    required _i40.VoidCallback tapFavourite,
    required bool isFavorite,
    required _i40.VoidCallback onBackPress,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          FlipTextNewRoute.name,
          args: FlipTextNewRouteArgs(
            key: key,
            media: media,
            onComplete: onComplete,
            tapFavourite: tapFavourite,
            isFavorite: isFavorite,
            onBackPress: onBackPress,
          ),
          initialChildren: children,
        );

  static const String name = 'FlipTextNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FlipTextNewRouteArgs>();
      return _i6.FlipTextNewPage(
        key: args.key,
        media: args.media,
        onComplete: args.onComplete,
        tapFavourite: args.tapFavourite,
        isFavorite: args.isFavorite,
        onBackPress: args.onBackPress,
      );
    },
  );
}

class FlipTextNewRouteArgs {
  const FlipTextNewRouteArgs({
    this.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
  });

  final _i40.Key? key;

  final _i42.Media media;

  final _i40.VoidCallback onComplete;

  final _i40.VoidCallback tapFavourite;

  final bool isFavorite;

  final _i40.VoidCallback onBackPress;

  @override
  String toString() {
    return 'FlipTextNewRouteArgs{key: $key, media: $media, onComplete: $onComplete, tapFavourite: $tapFavourite, isFavorite: $isFavorite, onBackPress: $onBackPress}';
  }
}

/// generated route for
/// [_i7.ForgotPasswordPage]
class ForgotPasswordRoute extends _i39.PageRouteInfo<void> {
  const ForgotPasswordRoute({List<_i39.PageRouteInfo>? children})
      : super(
          ForgotPasswordRoute.name,
          initialChildren: children,
        );

  static const String name = 'ForgotPasswordRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i7.ForgotPasswordPage();
    },
  );
}

/// generated route for
/// [_i8.GotchaWebViewPage]
class GotchaWebViewRoute extends _i39.PageRouteInfo<GotchaWebViewRouteArgs> {
  GotchaWebViewRoute({
    _i40.Key? key,
    required String? url,
    required String? title,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          GotchaWebViewRoute.name,
          args: GotchaWebViewRouteArgs(
            key: key,
            url: url,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'GotchaWebViewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GotchaWebViewRouteArgs>();
      return _i8.GotchaWebViewPage(
        key: args.key,
        url: args.url,
        title: args.title,
      );
    },
  );
}

class GotchaWebViewRouteArgs {
  const GotchaWebViewRouteArgs({
    this.key,
    required this.url,
    required this.title,
  });

  final _i40.Key? key;

  final String? url;

  final String? title;

  @override
  String toString() {
    return 'GotchaWebViewRouteArgs{key: $key, url: $url, title: $title}';
  }
}

/// generated route for
/// [_i9.GymHistoryPage]
class GymHistoryRoute extends _i39.PageRouteInfo<GymHistoryRouteArgs> {
  GymHistoryRoute({
    _i40.Key? key,
    required String? selectedChip,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          GymHistoryRoute.name,
          args: GymHistoryRouteArgs(
            key: key,
            selectedChip: selectedChip,
          ),
          initialChildren: children,
        );

  static const String name = 'GymHistoryRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<GymHistoryRouteArgs>();
      return _i9.GymHistoryPage(
        key: args.key,
        selectedChip: args.selectedChip,
      );
    },
  );
}

class GymHistoryRouteArgs {
  const GymHistoryRouteArgs({
    this.key,
    required this.selectedChip,
  });

  final _i40.Key? key;

  final String? selectedChip;

  @override
  String toString() {
    return 'GymHistoryRouteArgs{key: $key, selectedChip: $selectedChip}';
  }
}

/// generated route for
/// [_i10.HelpSeekingPathwayPage]
class HelpSeekingPathwayRoute extends _i39.PageRouteInfo<void> {
  const HelpSeekingPathwayRoute({List<_i39.PageRouteInfo>? children})
      : super(
          HelpSeekingPathwayRoute.name,
          initialChildren: children,
        );

  static const String name = 'HelpSeekingPathwayRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i10.HelpSeekingPathwayPage();
    },
  );
}

/// generated route for
/// [_i11.HomePage]
class HomeRoute extends _i39.PageRouteInfo<HomeRouteArgs> {
  HomeRoute({
    _i40.Key? key,
    required int index,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          HomeRoute.name,
          args: HomeRouteArgs(
            key: key,
            index: index,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<HomeRouteArgs>();
      return _i11.HomePage(
        key: args.key,
        index: args.index,
      );
    },
  );
}

class HomeRouteArgs {
  const HomeRouteArgs({
    this.key,
    required this.index,
  });

  final _i40.Key? key;

  final int index;

  @override
  String toString() {
    return 'HomeRouteArgs{key: $key, index: $index}';
  }
}

/// generated route for
/// [_i12.ImageTextNewPage]
class ImageTextNewRoute extends _i39.PageRouteInfo<ImageTextNewRouteArgs> {
  ImageTextNewRoute({
    _i40.Key? key,
    required _i42.Media media,
    required _i40.VoidCallback onComplete,
    required _i40.VoidCallback tapFavourite,
    required bool isFavorite,
    required _i40.VoidCallback onBackPress,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          ImageTextNewRoute.name,
          args: ImageTextNewRouteArgs(
            key: key,
            media: media,
            onComplete: onComplete,
            tapFavourite: tapFavourite,
            isFavorite: isFavorite,
            onBackPress: onBackPress,
          ),
          initialChildren: children,
        );

  static const String name = 'ImageTextNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ImageTextNewRouteArgs>();
      return _i12.ImageTextNewPage(
        key: args.key,
        media: args.media,
        onComplete: args.onComplete,
        tapFavourite: args.tapFavourite,
        isFavorite: args.isFavorite,
        onBackPress: args.onBackPress,
      );
    },
  );
}

class ImageTextNewRouteArgs {
  const ImageTextNewRouteArgs({
    this.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
  });

  final _i40.Key? key;

  final _i42.Media media;

  final _i40.VoidCallback onComplete;

  final _i40.VoidCallback tapFavourite;

  final bool isFavorite;

  final _i40.VoidCallback onBackPress;

  @override
  String toString() {
    return 'ImageTextNewRouteArgs{key: $key, media: $media, onComplete: $onComplete, tapFavourite: $tapFavourite, isFavorite: $isFavorite, onBackPress: $onBackPress}';
  }
}

/// generated route for
/// [_i13.LoginPage]
class LoginRoute extends _i39.PageRouteInfo<void> {
  const LoginRoute({List<_i39.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i13.LoginPage();
    },
  );
}

/// generated route for
/// [_i14.MixedExercisePage]
class MixedExerciseRoute extends _i39.PageRouteInfo<MixedExerciseRouteArgs> {
  MixedExerciseRoute({
    _i40.Key? key,
    required String id,
    required bool notification,
    required String seriesId,
    required bool isLast,
    required bool isFirst,
    required bool isOverride,
    String exerciseId = '',
    List<_i39.PageRouteInfo>? children,
  }) : super(
          MixedExerciseRoute.name,
          args: MixedExerciseRouteArgs(
            key: key,
            id: id,
            notification: notification,
            seriesId: seriesId,
            isLast: isLast,
            isFirst: isFirst,
            isOverride: isOverride,
            exerciseId: exerciseId,
          ),
          initialChildren: children,
        );

  static const String name = 'MixedExerciseRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MixedExerciseRouteArgs>();
      return _i14.MixedExercisePage(
        key: args.key,
        id: args.id,
        notification: args.notification,
        seriesId: args.seriesId,
        isLast: args.isLast,
        isFirst: args.isFirst,
        isOverride: args.isOverride,
        exerciseId: args.exerciseId,
      );
    },
  );
}

class MixedExerciseRouteArgs {
  const MixedExerciseRouteArgs({
    this.key,
    required this.id,
    required this.notification,
    required this.seriesId,
    required this.isLast,
    required this.isFirst,
    required this.isOverride,
    this.exerciseId = '',
  });

  final _i40.Key? key;

  final String id;

  final bool notification;

  final String seriesId;

  final bool isLast;

  final bool isFirst;

  final bool isOverride;

  final String exerciseId;

  @override
  String toString() {
    return 'MixedExerciseRouteArgs{key: $key, id: $id, notification: $notification, seriesId: $seriesId, isLast: $isLast, isFirst: $isFirst, isOverride: $isOverride, exerciseId: $exerciseId}';
  }
}

/// generated route for
/// [_i15.NormalFeedPage]
class NormalFeedRoute extends _i39.PageRouteInfo<NormalFeedRouteArgs> {
  NormalFeedRoute({
    _i40.Key? key,
    required _i40.PageController homePageController,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          NormalFeedRoute.name,
          args: NormalFeedRouteArgs(
            key: key,
            homePageController: homePageController,
          ),
          initialChildren: children,
        );

  static const String name = 'NormalFeedRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NormalFeedRouteArgs>();
      return _i15.NormalFeedPage(
        key: args.key,
        homePageController: args.homePageController,
      );
    },
  );
}

class NormalFeedRouteArgs {
  const NormalFeedRouteArgs({
    this.key,
    required this.homePageController,
  });

  final _i40.Key? key;

  final _i40.PageController homePageController;

  @override
  String toString() {
    return 'NormalFeedRouteArgs{key: $key, homePageController: $homePageController}';
  }
}

/// generated route for
/// [_i16.NotificationAllowPage]
class NotificationAllowRoute extends _i39.PageRouteInfo<void> {
  const NotificationAllowRoute({List<_i39.PageRouteInfo>? children})
      : super(
          NotificationAllowRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationAllowRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i16.NotificationAllowPage();
    },
  );
}

/// generated route for
/// [_i17.NotificationPermissionPage]
class NotificationPermissionRoute extends _i39.PageRouteInfo<void> {
  const NotificationPermissionRoute({List<_i39.PageRouteInfo>? children})
      : super(
          NotificationPermissionRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationPermissionRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i17.NotificationPermissionPage();
    },
  );
}

/// generated route for
/// [_i18.OnBoardingThanksPage]
class OnBoardingThanksRoute
    extends _i39.PageRouteInfo<OnBoardingThanksRouteArgs> {
  OnBoardingThanksRoute({
    _i40.Key? key,
    required _i43.EmotionsDetailResponse? data,
    String? description,
    required String checkInTypeId,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          OnBoardingThanksRoute.name,
          args: OnBoardingThanksRouteArgs(
            key: key,
            data: data,
            description: description,
            checkInTypeId: checkInTypeId,
          ),
          initialChildren: children,
        );

  static const String name = 'OnBoardingThanksRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OnBoardingThanksRouteArgs>();
      return _i18.OnBoardingThanksPage(
        key: args.key,
        data: args.data,
        description: args.description,
        checkInTypeId: args.checkInTypeId,
      );
    },
  );
}

class OnBoardingThanksRouteArgs {
  const OnBoardingThanksRouteArgs({
    this.key,
    required this.data,
    this.description,
    required this.checkInTypeId,
  });

  final _i40.Key? key;

  final _i43.EmotionsDetailResponse? data;

  final String? description;

  final String checkInTypeId;

  @override
  String toString() {
    return 'OnBoardingThanksRouteArgs{key: $key, data: $data, description: $description, checkInTypeId: $checkInTypeId}';
  }
}

/// generated route for
/// [_i19.OnboardingDealingWithPage]
class OnboardingDealingWithRoute
    extends _i39.PageRouteInfo<OnboardingDealingWithRouteArgs> {
  OnboardingDealingWithRoute({
    _i40.Key? key,
    _i43.EmotionsDetailResponse? data,
    required String checkInTypeId,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          OnboardingDealingWithRoute.name,
          args: OnboardingDealingWithRouteArgs(
            key: key,
            data: data,
            checkInTypeId: checkInTypeId,
          ),
          initialChildren: children,
        );

  static const String name = 'OnboardingDealingWithRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OnboardingDealingWithRouteArgs>();
      return _i19.OnboardingDealingWithPage(
        key: args.key,
        data: args.data,
        checkInTypeId: args.checkInTypeId,
      );
    },
  );
}

class OnboardingDealingWithRouteArgs {
  const OnboardingDealingWithRouteArgs({
    this.key,
    this.data,
    required this.checkInTypeId,
  });

  final _i40.Key? key;

  final _i43.EmotionsDetailResponse? data;

  final String checkInTypeId;

  @override
  String toString() {
    return 'OnboardingDealingWithRouteArgs{key: $key, data: $data, checkInTypeId: $checkInTypeId}';
  }
}

/// generated route for
/// [_i20.OnboardingFeelingPage]
class OnboardingFeelingRoute extends _i39.PageRouteInfo<void> {
  const OnboardingFeelingRoute({List<_i39.PageRouteInfo>? children})
      : super(
          OnboardingFeelingRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnboardingFeelingRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i20.OnboardingFeelingPage();
    },
  );
}

/// generated route for
/// [_i21.OnboardingGoalPage]
class OnboardingGoalRoute extends _i39.PageRouteInfo<void> {
  const OnboardingGoalRoute({List<_i39.PageRouteInfo>? children})
      : super(
          OnboardingGoalRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnboardingGoalRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i21.OnboardingGoalPage();
    },
  );
}

/// generated route for
/// [_i22.OnboardingMentalBoostsPage]
class OnboardingMentalBoostsRoute
    extends _i39.PageRouteInfo<OnboardingMentalBoostsRouteArgs> {
  OnboardingMentalBoostsRoute({
    _i40.Key? key,
    _i43.EmotionsDetailResponse? data,
    required String checkInTypeId,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          OnboardingMentalBoostsRoute.name,
          args: OnboardingMentalBoostsRouteArgs(
            key: key,
            data: data,
            checkInTypeId: checkInTypeId,
          ),
          initialChildren: children,
        );

  static const String name = 'OnboardingMentalBoostsRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OnboardingMentalBoostsRouteArgs>();
      return _i22.OnboardingMentalBoostsPage(
        key: args.key,
        data: args.data,
        checkInTypeId: args.checkInTypeId,
      );
    },
  );
}

class OnboardingMentalBoostsRouteArgs {
  const OnboardingMentalBoostsRouteArgs({
    this.key,
    this.data,
    required this.checkInTypeId,
  });

  final _i40.Key? key;

  final _i43.EmotionsDetailResponse? data;

  final String checkInTypeId;

  @override
  String toString() {
    return 'OnboardingMentalBoostsRouteArgs{key: $key, data: $data, checkInTypeId: $checkInTypeId}';
  }
}

/// generated route for
/// [_i23.OnboardingWelcomePage]
class OnboardingWelcomeRoute extends _i39.PageRouteInfo<void> {
  const OnboardingWelcomeRoute({List<_i39.PageRouteInfo>? children})
      : super(
          OnboardingWelcomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnboardingWelcomeRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i23.OnboardingWelcomePage();
    },
  );
}

/// generated route for
/// [_i24.PersonaliseAccountPage]
class PersonaliseAccountRoute
    extends _i39.PageRouteInfo<PersonaliseAccountRouteArgs> {
  PersonaliseAccountRoute({
    _i40.Key? key,
    required bool? isPersonalise,
    required bool? isInfo,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          PersonaliseAccountRoute.name,
          args: PersonaliseAccountRouteArgs(
            key: key,
            isPersonalise: isPersonalise,
            isInfo: isInfo,
          ),
          initialChildren: children,
        );

  static const String name = 'PersonaliseAccountRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PersonaliseAccountRouteArgs>();
      return _i24.PersonaliseAccountPage(
        key: args.key,
        isPersonalise: args.isPersonalise,
        isInfo: args.isInfo,
      );
    },
  );
}

class PersonaliseAccountRouteArgs {
  const PersonaliseAccountRouteArgs({
    this.key,
    required this.isPersonalise,
    required this.isInfo,
  });

  final _i40.Key? key;

  final bool? isPersonalise;

  final bool? isInfo;

  @override
  String toString() {
    return 'PersonaliseAccountRouteArgs{key: $key, isPersonalise: $isPersonalise, isInfo: $isInfo}';
  }
}

/// generated route for
/// [_i25.ProfilePage]
class ProfileRoute extends _i39.PageRouteInfo<void> {
  const ProfileRoute({List<_i39.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i25.ProfilePage();
    },
  );
}

/// generated route for
/// [_i26.ReflectionCompleteNewPage]
class ReflectionCompleteNewRoute
    extends _i39.PageRouteInfo<ReflectionCompleteNewRouteArgs> {
  ReflectionCompleteNewRoute({
    _i40.Key? key,
    required List<_i44.Answer> answers,
    required int totalBars,
    required String? refId,
    required bool feedback,
    required bool isVillage,
    required bool notification,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          ReflectionCompleteNewRoute.name,
          args: ReflectionCompleteNewRouteArgs(
            key: key,
            answers: answers,
            totalBars: totalBars,
            refId: refId,
            feedback: feedback,
            isVillage: isVillage,
            notification: notification,
          ),
          initialChildren: children,
        );

  static const String name = 'ReflectionCompleteNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ReflectionCompleteNewRouteArgs>();
      return _i26.ReflectionCompleteNewPage(
        key: args.key,
        answers: args.answers,
        totalBars: args.totalBars,
        refId: args.refId,
        feedback: args.feedback,
        isVillage: args.isVillage,
        notification: args.notification,
      );
    },
  );
}

class ReflectionCompleteNewRouteArgs {
  const ReflectionCompleteNewRouteArgs({
    this.key,
    required this.answers,
    required this.totalBars,
    required this.refId,
    required this.feedback,
    required this.isVillage,
    required this.notification,
  });

  final _i40.Key? key;

  final List<_i44.Answer> answers;

  final int totalBars;

  final String? refId;

  final bool feedback;

  final bool isVillage;

  final bool notification;

  @override
  String toString() {
    return 'ReflectionCompleteNewRouteArgs{key: $key, answers: $answers, totalBars: $totalBars, refId: $refId, feedback: $feedback, isVillage: $isVillage, notification: $notification}';
  }
}

/// generated route for
/// [_i27.ReflectionMultiNewPage]
class ReflectionMultiNewRoute
    extends _i39.PageRouteInfo<ReflectionMultiNewRouteArgs> {
  ReflectionMultiNewRoute({
    _i40.Key? key,
    required List<_i42.ReflectionQuestion>? reflection,
    required int index,
    required String? refId,
    required bool feedback,
    required bool isVillage,
    required bool notification,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          ReflectionMultiNewRoute.name,
          args: ReflectionMultiNewRouteArgs(
            key: key,
            reflection: reflection,
            index: index,
            refId: refId,
            feedback: feedback,
            isVillage: isVillage,
            notification: notification,
          ),
          initialChildren: children,
        );

  static const String name = 'ReflectionMultiNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ReflectionMultiNewRouteArgs>();
      return _i27.ReflectionMultiNewPage(
        key: args.key,
        reflection: args.reflection,
        index: args.index,
        refId: args.refId,
        feedback: args.feedback,
        isVillage: args.isVillage,
        notification: args.notification,
      );
    },
  );
}

class ReflectionMultiNewRouteArgs {
  const ReflectionMultiNewRouteArgs({
    this.key,
    required this.reflection,
    required this.index,
    required this.refId,
    required this.feedback,
    required this.isVillage,
    required this.notification,
  });

  final _i40.Key? key;

  final List<_i42.ReflectionQuestion>? reflection;

  final int index;

  final String? refId;

  final bool feedback;

  final bool isVillage;

  final bool notification;

  @override
  String toString() {
    return 'ReflectionMultiNewRouteArgs{key: $key, reflection: $reflection, index: $index, refId: $refId, feedback: $feedback, isVillage: $isVillage, notification: $notification}';
  }
}

/// generated route for
/// [_i28.ReflectionTextNewPage]
class ReflectionTextNewRoute
    extends _i39.PageRouteInfo<ReflectionTextNewRouteArgs> {
  ReflectionTextNewRoute({
    _i40.Key? key,
    required List<_i42.ReflectionQuestion>? reflection,
    required int index,
    required String? refId,
    required bool feedback,
    required bool isVillage,
    required bool notification,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          ReflectionTextNewRoute.name,
          args: ReflectionTextNewRouteArgs(
            key: key,
            reflection: reflection,
            index: index,
            refId: refId,
            feedback: feedback,
            isVillage: isVillage,
            notification: notification,
          ),
          initialChildren: children,
        );

  static const String name = 'ReflectionTextNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ReflectionTextNewRouteArgs>();
      return _i28.ReflectionTextNewPage(
        key: args.key,
        reflection: args.reflection,
        index: args.index,
        refId: args.refId,
        feedback: args.feedback,
        isVillage: args.isVillage,
        notification: args.notification,
      );
    },
  );
}

class ReflectionTextNewRouteArgs {
  const ReflectionTextNewRouteArgs({
    this.key,
    required this.reflection,
    required this.index,
    required this.refId,
    required this.feedback,
    required this.isVillage,
    required this.notification,
  });

  final _i40.Key? key;

  final List<_i42.ReflectionQuestion>? reflection;

  final int index;

  final String? refId;

  final bool feedback;

  final bool isVillage;

  final bool notification;

  @override
  String toString() {
    return 'ReflectionTextNewRouteArgs{key: $key, reflection: $reflection, index: $index, refId: $refId, feedback: $feedback, isVillage: $isVillage, notification: $notification}';
  }
}

/// generated route for
/// [_i29.SelfAssesmentResultPage]
class SelfAssesmentResultRoute
    extends _i39.PageRouteInfo<SelfAssesmentResultRouteArgs> {
  SelfAssesmentResultRoute({
    _i40.Key? key,
    required Map<String, int> selectedAnswers,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          SelfAssesmentResultRoute.name,
          args: SelfAssesmentResultRouteArgs(
            key: key,
            selectedAnswers: selectedAnswers,
          ),
          initialChildren: children,
        );

  static const String name = 'SelfAssesmentResultRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelfAssesmentResultRouteArgs>();
      return _i29.SelfAssesmentResultPage(
        key: args.key,
        selectedAnswers: args.selectedAnswers,
      );
    },
  );
}

class SelfAssesmentResultRouteArgs {
  const SelfAssesmentResultRouteArgs({
    this.key,
    required this.selectedAnswers,
  });

  final _i40.Key? key;

  final Map<String, int> selectedAnswers;

  @override
  String toString() {
    return 'SelfAssesmentResultRouteArgs{key: $key, selectedAnswers: $selectedAnswers}';
  }
}

/// generated route for
/// [_i30.SelfAssesmentWelcomePage]
class SelfAssesmentWelcomeRoute extends _i39.PageRouteInfo<void> {
  const SelfAssesmentWelcomeRoute({List<_i39.PageRouteInfo>? children})
      : super(
          SelfAssesmentWelcomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'SelfAssesmentWelcomeRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i30.SelfAssesmentWelcomePage();
    },
  );
}

/// generated route for
/// [_i31.SelfAssessmentStartPage]
class SelfAssessmentStartRoute
    extends _i39.PageRouteInfo<SelfAssessmentStartRouteArgs> {
  SelfAssessmentStartRoute({
    _i40.Key? key,
    required List<_i45.Datum>? assessments,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          SelfAssessmentStartRoute.name,
          args: SelfAssessmentStartRouteArgs(
            key: key,
            assessments: assessments,
          ),
          initialChildren: children,
        );

  static const String name = 'SelfAssessmentStartRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SelfAssessmentStartRouteArgs>();
      return _i31.SelfAssessmentStartPage(
        key: args.key,
        assessments: args.assessments,
      );
    },
  );
}

class SelfAssessmentStartRouteArgs {
  const SelfAssessmentStartRouteArgs({
    this.key,
    required this.assessments,
  });

  final _i40.Key? key;

  final List<_i45.Datum>? assessments;

  @override
  String toString() {
    return 'SelfAssessmentStartRouteArgs{key: $key, assessments: $assessments}';
  }
}

/// generated route for
/// [_i32.SignUpPage]
class SignUpRoute extends _i39.PageRouteInfo<void> {
  const SignUpRoute({List<_i39.PageRouteInfo>? children})
      : super(
          SignUpRoute.name,
          initialChildren: children,
        );

  static const String name = 'SignUpRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i32.SignUpPage();
    },
  );
}

/// generated route for
/// [_i33.SplashPage]
class SplashRoute extends _i39.PageRouteInfo<void> {
  const SplashRoute({List<_i39.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i33.SplashPage();
    },
  );
}

/// generated route for
/// [_i34.TextAnimationNewPage]
class TextAnimationNewRoute
    extends _i39.PageRouteInfo<TextAnimationNewRouteArgs> {
  TextAnimationNewRoute({
    _i40.Key? key,
    required _i42.Media media,
    required _i40.VoidCallback onComplete,
    required _i40.VoidCallback tapFavourite,
    required bool isFavorite,
    required _i40.VoidCallback onBackPress,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          TextAnimationNewRoute.name,
          args: TextAnimationNewRouteArgs(
            key: key,
            media: media,
            onComplete: onComplete,
            tapFavourite: tapFavourite,
            isFavorite: isFavorite,
            onBackPress: onBackPress,
          ),
          initialChildren: children,
        );

  static const String name = 'TextAnimationNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TextAnimationNewRouteArgs>();
      return _i34.TextAnimationNewPage(
        key: args.key,
        media: args.media,
        onComplete: args.onComplete,
        tapFavourite: args.tapFavourite,
        isFavorite: args.isFavorite,
        onBackPress: args.onBackPress,
      );
    },
  );
}

class TextAnimationNewRouteArgs {
  const TextAnimationNewRouteArgs({
    this.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
  });

  final _i40.Key? key;

  final _i42.Media media;

  final _i40.VoidCallback onComplete;

  final _i40.VoidCallback tapFavourite;

  final bool isFavorite;

  final _i40.VoidCallback onBackPress;

  @override
  String toString() {
    return 'TextAnimationNewRouteArgs{key: $key, media: $media, onComplete: $onComplete, tapFavourite: $tapFavourite, isFavorite: $isFavorite, onBackPress: $onBackPress}';
  }
}

/// generated route for
/// [_i35.VideoPlayerNewPage]
class VideoPlayerNewRoute extends _i39.PageRouteInfo<VideoPlayerNewRouteArgs> {
  VideoPlayerNewRoute({
    _i40.Key? key,
    required _i42.Media media,
    required _i40.VoidCallback onComplete,
    required _i40.VoidCallback tapFavourite,
    required bool isFavorite,
    required _i40.VoidCallback onBackPress,
    String? title = '',
    String? subTitle = '',
    List<_i42.Category>? categories = const [],
    List<_i39.PageRouteInfo>? children,
  }) : super(
          VideoPlayerNewRoute.name,
          args: VideoPlayerNewRouteArgs(
            key: key,
            media: media,
            onComplete: onComplete,
            tapFavourite: tapFavourite,
            isFavorite: isFavorite,
            onBackPress: onBackPress,
            title: title,
            subTitle: subTitle,
            categories: categories,
          ),
          initialChildren: children,
        );

  static const String name = 'VideoPlayerNewRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<VideoPlayerNewRouteArgs>();
      return _i35.VideoPlayerNewPage(
        key: args.key,
        media: args.media,
        onComplete: args.onComplete,
        tapFavourite: args.tapFavourite,
        isFavorite: args.isFavorite,
        onBackPress: args.onBackPress,
        title: args.title,
        subTitle: args.subTitle,
        categories: args.categories,
      );
    },
  );
}

class VideoPlayerNewRouteArgs {
  const VideoPlayerNewRouteArgs({
    this.key,
    required this.media,
    required this.onComplete,
    required this.tapFavourite,
    required this.isFavorite,
    required this.onBackPress,
    this.title = '',
    this.subTitle = '',
    this.categories = const [],
  });

  final _i40.Key? key;

  final _i42.Media media;

  final _i40.VoidCallback onComplete;

  final _i40.VoidCallback tapFavourite;

  final bool isFavorite;

  final _i40.VoidCallback onBackPress;

  final String? title;

  final String? subTitle;

  final List<_i42.Category>? categories;

  @override
  String toString() {
    return 'VideoPlayerNewRouteArgs{key: $key, media: $media, onComplete: $onComplete, tapFavourite: $tapFavourite, isFavorite: $isFavorite, onBackPress: $onBackPress, title: $title, subTitle: $subTitle, categories: $categories}';
  }
}

/// generated route for
/// [_i36.VillageHomePage]
class VillageHomeRoute extends _i39.PageRouteInfo<void> {
  const VillageHomeRoute({List<_i39.PageRouteInfo>? children})
      : super(
          VillageHomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'VillageHomeRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i36.VillageHomePage();
    },
  );
}

/// generated route for
/// [_i37.WelcomeFeedPage]
class WelcomeFeedRoute extends _i39.PageRouteInfo<void> {
  const WelcomeFeedRoute({List<_i39.PageRouteInfo>? children})
      : super(
          WelcomeFeedRoute.name,
          initialChildren: children,
        );

  static const String name = 'WelcomeFeedRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      return const _i37.WelcomeFeedPage();
    },
  );
}

/// generated route for
/// [_i38.WorkoutPage]
class WorkoutRoute extends _i39.PageRouteInfo<WorkoutRouteArgs> {
  WorkoutRoute({
    _i40.Key? key,
    required String seriesId,
    List<_i39.PageRouteInfo>? children,
  }) : super(
          WorkoutRoute.name,
          args: WorkoutRouteArgs(
            key: key,
            seriesId: seriesId,
          ),
          initialChildren: children,
        );

  static const String name = 'WorkoutRoute';

  static _i39.PageInfo page = _i39.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WorkoutRouteArgs>();
      return _i38.WorkoutPage(
        key: args.key,
        seriesId: args.seriesId,
      );
    },
  );
}

class WorkoutRouteArgs {
  const WorkoutRouteArgs({
    this.key,
    required this.seriesId,
  });

  final _i40.Key? key;

  final String seriesId;

  @override
  String toString() {
    return 'WorkoutRouteArgs{key: $key, seriesId: $seriesId}';
  }
}
