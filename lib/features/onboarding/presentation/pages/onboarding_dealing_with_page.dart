import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart'
    as d;
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/widgets/onboarding_sub_emotion_section.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';

@RoutePage()
class OnboardingDealingWithPage extends StatefulWidget {
  final EmotionsDetailResponse? data;
  final String checkInTypeId;

  const OnboardingDealingWithPage({
    super.key,
    this.data,
    required this.checkInTypeId,
  });

  @override
  State<OnboardingDealingWithPage> createState() =>
      _OnboardingDealingWithPageState();
}

class _OnboardingDealingWithPageState extends State<OnboardingDealingWithPage> {
  List<d.Answer> sampleAnswers2 = [
    d.Answer(answerId: '1', answer: '👾 Sleep troubles'),
  ];
  void _onSelectionChanged(List<Question> selectedQuestions) {
    // Print selected answers (for demo purposes)
    for (var question in selectedQuestions) {
      info('Selected Answer: ${question.answer}, Other: ${question.other}');
    }
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Dealing With Page',
      properties: {'Code': 'screen_view.onboarding_dealing_with_page'},
    );
  }

  @override
  Widget build(BuildContext context) {
    var selectedItem =
        widget.data?.data?.firstWhere((item) => item.orderOfQuestion == 2);
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.grey,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        behavior: HitTestBehavior.translucent,
        child: Padding(
          padding: EdgeInsets.only(
            top: isIos ? 4 : 8,
            left: 8,
            right: 8,
          ),
          child: Column(
            children: [
              const AppHeader(
                title: 'Getting started',
                currentStep: 3,
                totalSteps: 4,
              ),
              Expanded(
                child: Container(
                  color: AppColors.navy,
                  child: Container(
                    height: size.height,
                    width: size.width,
                    padding: EdgeInsets.only(bottom: isIos ? 80 : 56),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      color: AppColors.grey,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Gap(12),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 24,
                              right: 24,
                              top: 16,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  selectedItem?.description ?? 'N/A',
                                  style: textTheme.ralewayRegular
                                      .copyWith(fontSize: 14),
                                ),
                                const Gap(4),
                                Text(
                                  selectedItem?.question ?? 'N/A',
                                  style: textTheme.ralewaySemiBold.copyWith(
                                    fontSize: 17,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Gap(4),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: OnboardingSubEmotionSection(
                              optional: false,
                              title: '',
                              items: selectedItem?.answers ?? [],
                              checkInTypeId: widget.checkInTypeId,
                              questionId: selectedItem?.questionId ?? '',
                            ),
                          ),
                          const Gap(120),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: isKeyboardOpen
          ? const SizedBox()
          : Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(
                vertical: 24,
                horizontal: 32,
              ),
              child: PrimaryButton(
                text: 'Next',
                isEnabled: true,
                onPressed: () {
                  sl<MixpanelService>().trackButtonClick('Next', properties: {
                    'Page': 'Onboarding Dealing With Page',
                    'Code': 'click.onboarding_dealing_with_page.next'
                  });

                  context.pushRoute(
                    OnboardingMentalBoostsRoute(
                      data: widget.data,
                      checkInTypeId: widget.checkInTypeId,
                    ),
                  );
                  // context.navigateTo(const OnboardingPage3IntroRoute());
                },
              ),
            ),
    );
  }
}
