import 'package:auto_route/auto_route.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/extensions/url_extensions.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/explore/domain/entities/search_result.dart';
import 'package:gotcha_mfg_app/features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'package:gotcha_mfg_app/features/explore/presentation/cubits/explore_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/network_image_indicator.dart';
import 'package:gotcha_mfg_app/shared/widgets/no_data.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/app_print.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../../../exercise/presentation/pages/mixed_exercise_page.dart';
import '../../../favourites/presentation/bloc/favourite/favourites_cubit.dart';
import '../widgets/build_popup_menu.dart';
import '../widgets/explore_no_data.dart';
import '../../../home/<USER>/widgets/exercise_card.dart';

@RoutePage()
class ExplorePage extends StatefulWidget {
  const ExplorePage({super.key});

  @override
  State<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends State<ExplorePage> {
  bool _isSearching = false;
  final _searchController = TextEditingController();
  final GlobalKey _searchBarKey = GlobalKey(); // GlobalKey for search bar
  bool _isScrolling = false; // Track if scrolling is in progress
  final ScrollController _searchSuggestionsScrollController =
      ScrollController(); // Add this line

  final List<Menu> _menus = [
    Menu(name: 'All', checked: true, selectedVenue: 1),
    Menu(name: 'Workouts', checked: false, selectedVenue: 2),
    Menu(name: 'Exercises', checked: false, selectedVenue: 3),
    Menu(name: 'Completed', checked: false, selectedVenue: 4),
  ];

  _reset() {
    for (var menu in _menus) {
      menu.checked = false;
    }
    _menus[0].checked = true;
    _params = FilteredResponseParams(
      page: 1,
    );
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Explore Page',
      properties: {'Code': 'screen_view.login_page'},
    );
    context.read<ExploreCubit>().getCategoriesAndWorkouts();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check if the route is being reactivated (user navigated back)
    // if (ModalRoute.of(context)?.isCurrent ?? false) {
    // setState(() {
    //   _isSearching = false;
    // });
    // }
  }

  bool _showCategoryAndWorkouts = true;

  void _updateSelectedItem(int index) {
    setState(() {
      for (var menu in _menus) {
        menu.checked = false;
      }
      _menus[index].checked = true;
    });
  }

  var _params = FilteredResponseParams(
    page: 1,
  );

  @override
  void dispose() {
    _searchSuggestionsScrollController
        .dispose(); // Add this line to dispose the controller
    super.dispose();
  }

  Widget _buildSkeletonLoader(Size size, TextTheme textTheme) {
    return Skeletonizer(
            effect:  const ShimmerEffect(),
            enableSwitchAnimation: true,
      enabled: true,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            left: 8,
            right: 8,
            top: isIos ? 4 : 8,
            bottom: isIos ? 32 : 32,
          ),
          child: ListView(
            physics: const NeverScrollableScrollPhysics(),
            children: [
              // Search Bar Container Skeleton
              Container(
                decoration: const BoxDecoration(
                  color: AppColors.navy,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                height: 164,
                width: size.width,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // "Explore" title skeleton
                      Container(
                        height: 24,
                        width: 100,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const Gap(16),
                      // Search bar skeleton
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(30),
                          border: Border.all(color: AppColors.midBlue, width: 1.5),
                        ),
                        child: Row(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(left: 12),
                              child: Icon(
                                Icons.search,
                                color: AppColors.navy,
                              ),
                            ),
                            const Gap(12),
                            Expanded(
                              child: Container(
                                height: 16,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                            const Gap(12),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Gap(8),
              // "Explore by category" title skeleton
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                child: Container(
                  height: 20,
                  width: 180,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              // Category grid skeleton
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const ClampingScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 10,
                    childAspectRatio: 1.4,
                  ),
                  itemCount: 6, // Show 6 skeleton category cards
                  itemBuilder: (context, index) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Center(
                        child: Container(
                          height: 16,
                          width: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const Gap(24),
              // "Recommended workouts" title skeleton
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Container(
                  height: 20,
                  width: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const Gap(16),
              // Recommended workouts list skeleton
              SizedBox(
                height: 140,
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  scrollDirection: Axis.vertical,
                  itemCount: 3, // Show 3 skeleton workout cards
                  itemBuilder: (context, index) {
                    return Container(
                      width: 280,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            // Thumbnail skeleton
                            Container(
                              width: 108,
                              height: 108,
                              decoration: BoxDecoration(
                                color: Colors.grey[400],
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            const Gap(12),
                            // Content skeleton
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    height: 16,
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[400],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                  const Gap(8),
                                  Container(
                                    height: 14,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[400],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                  const Gap(8),
                                  Container(
                                    height: 12,
                                    width: 80,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[400],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => const Gap(12),
                ),
              ),
              const Gap(144),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;

    return PopScope(
      canPop: false,
      child: BlocListener<FavouritesCubit, GetFavouriteState>(
        listener: (context, state) {
          if (state is FavouritesToggled) {
            context.read<ExploreCubit>().getFilteredExercises(_params);
          }
          if (state is FeatureNotInterestedSuccess) {
            context.read<ExploreCubit>().getFilteredExercises(_params);
          }
        },
        child: BlocConsumer<ExploreCubit, ExploreState>(
          listener: (context, state) {
            if (state is ExploreError) {
              SnackBarService.error(
                context: context,
                message: state.message,
              );
            }
          },
          builder: (context, state) {
            if (state is ExploreLoading) {
              return _buildSkeletonLoader(size, textTheme);
            }
            if (state is ExploreError) {
              return RetryWidget(
                color: Colors.white,
                onRetry: () {
                  context.read<ExploreCubit>().getCategoriesAndWorkouts();
                },
              );
            }
            if (state is ExploreLoaded) {
              final categories = state.categoriesResponse?.data;
              final workouts = state.workoutsResponse?.data;
              final filteredList = state.filteredResponse != null
                  ? CombineExercisesAndWorkouts().call(state.filteredResponse!)
                  : [];
              final searchSuggestions = state.searchSuggestions != null
                  ? CombineExercisesAndWorkouts().call(state.searchSuggestions!)
                  : [];
              return Scaffold(
                appBar: AppBar(
                  toolbarHeight: 0,
                  elevation: 0,
                  systemOverlayStyle: const SystemUiOverlayStyle(
                    statusBarColor: Colors.white,
                    systemNavigationBarIconBrightness: Brightness.dark,
                    statusBarBrightness: Brightness.light,
                    statusBarIconBrightness: Brightness.dark,
                    systemNavigationBarColor: Colors.white,
                  ),
                ),
                body: SafeArea(
                  child: GestureDetector(
                    // GestureDetector wrapping the body
                    onTapDown: (details) {
                      if (!_isScrolling) {
                        // Check if the search field is empty when tapping outside
                        if (_searchController.text.isEmpty) {
                          // If empty, perform the clear action
                          setState(() {
                            _reset(); // Reset filters and state
                            _isSearching = false;
                            _showCategoryAndWorkouts = true;
                          });
                        }
                        // Always dismiss the keyboard
                        FocusScope.of(context).unfocus();
                      }
                      setState(() {
                        _isScrolling = false; // Reset scroll flag
                      });
                    },
                    onVerticalDragStart: (details) {
                      setState(() {
                        _isScrolling =
                            true; // Set scroll flag to true on drag start
                      });
                    },
                    onVerticalDragEnd: (details) {
                      setState(() {
                        _isScrolling = false; // Reset scroll flag on drag end
                      });
                    },
                    onVerticalDragCancel: () {
                      setState(() {
                        _isScrolling =
                            false; // Reset scroll flag on drag cancel
                      });
                    },
                    behavior: HitTestBehavior.translucent,
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: 8,
                        right: 8,
                        top: isIos ? 4 : 8,
                        bottom: isIos ? 32 : 32,
                      ),
                      child: Stack(
                        children: [
                          // Background: Categories Grid
                          ListView(
                            keyboardDismissBehavior:
                                ScrollViewKeyboardDismissBehavior.onDrag,
                            physics: const NeverScrollableScrollPhysics(),
                            children: [
                              // Search Bar
                              // ... (rest of your ListView and widgets, same as before)
                              Container(
                                key: _searchBarKey,
                                decoration: const BoxDecoration(
                                  color: AppColors.navy,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30),
                                    topRight: Radius.circular(30),
                                  ),
                                ),
                                height: 164,
                                width: size.width,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Explore',
                                        style:
                                            textTheme.sectionHeading.copyWith(
                                          color: Colors.white,
                                        ),
                                      ),
                                      const Gap(16),
                                      CustomSearchBar(
                                        // key: _searchBarKey,
                                        width: size.width,
                                        controller: _searchController,
                                        onSearchChanged: (text) {
                                          setState(() {
                                            _isSearching = text.isNotEmpty;
                                          });
                                          info("qwe1231: $_isSearching");
                                          _params = FilteredResponseParams(
                                            search: text,
                                            page: 1,
                                          );
                                          context
                                              .read<ExploreCubit>()
                                              .getSearchSuggestions(
                                                _params,
                                              );
                                        },
                                        isCategoriesShown:
                                            _showCategoryAndWorkouts,
                                        onSubmitted: (val) {
                                          if (val.isEmpty) {
                                            setState(() {
                                              _showCategoryAndWorkouts = true;
                                              _isSearching = false; //edittt
                                              _reset();
                                            });
                                            return;
                                          }
                                          sl<MixpanelService>()
                                              .trackSearchQuery(
                                            'Explore_Page',
                                            val,
                                          );
                                          setState(() {
                                            _showCategoryAndWorkouts = false;
                                            _isSearching = false; //edittt
                                          });
                                          info("qwe1232: $_isSearching");
                                          _params = FilteredResponseParams(
                                            search: val,
                                            page: 1,
                                          );
                                          //  context
                                          //     .read<ExploreCubit>()
                                          //     .getSearchSuggestions(
                                          //       _params,
                                          //     );
                                          context
                                              .read<ExploreCubit>()
                                              .getFilteredExercises(
                                                _params,
                                              );
                                        },
                                        onClear: () {
                                          setState(() {
                                            _reset();
                                            _isSearching = false; //edittt
                                            _showCategoryAndWorkouts = true;
                                          });
                                        },
                                        textStyle:
                                            textTheme.ralewayMedium.copyWith(
                                          fontSize: 12,
                                          color: AppColors.navy,
                                        ),
                                        hintStyle:
                                            textTheme.ralewayMedium.copyWith(
                                          fontSize: 12,
                                          color: AppColors.navy,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // ... (rest of your Container and widgets, same as before)
                              Container(
                                color: AppColors.navy,
                                height: size.height - 196,
                                child: _showCategoryAndWorkouts == true
                                    ? Container(
                                        decoration: const BoxDecoration(
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(30),
                                            topRight: Radius.circular(30),
                                          ),
                                          color: AppColors.grey,
                                        ),
                                        child: SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Gap(8),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 24,
                                                  vertical: 16,
                                                ),
                                                child: Text(
                                                  'Explore by category',
                                                  style: textTheme
                                                      .ralewaySemiBold
                                                      .copyWith(
                                                    fontSize: 17,
                                                    color: AppColors.navy,
                                                  ),
                                                ),
                                              ),
                                              Column(
                                                children: [
                                                  GridView.builder(
                                                    shrinkWrap: true,
                                                    physics:
                                                        const ClampingScrollPhysics(),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 24.0),
                                                    gridDelegate:
                                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount: 2,
                                                      crossAxisSpacing: 8,
                                                      mainAxisSpacing: 10,
                                                      childAspectRatio: 1.4,
                                                    ),
                                                    itemCount:
                                                        categories?.length ?? 0,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final category =
                                                          categories?[index];
                                                      return CategoryCard(
                                                        imageUrl: category
                                                                ?.imageUrl ??
                                                            '',
                                                        categoryName:
                                                            category?.name ??
                                                                '',
                                                        onTap: () {
                                                          setState(() {
                                                            _showCategoryAndWorkouts =
                                                                false;
                                                            _searchController
                                                                .text = category
                                                                    ?.name ??
                                                                '';
                                                          });
                                                          _params =
                                                              FilteredResponseParams(
                                                            categoryId:
                                                                category?.id ??
                                                                    '',
                                                            page: 1,
                                                          );
                                                          context
                                                              .read<
                                                                  ExploreCubit>()
                                                              .getFilteredExercises(
                                                                _params,
                                                              );
                                                          // mixpanel
                                                          sl<MixpanelService>()
                                                              .trackButtonClick(
                                                                  'Category Tapped',
                                                                  properties: {
                                                                'Page':
                                                                    'Explore Page',
                                                                'Code':
                                                                    'click.explore_page.category_tapped',
                                                                'Category Name':
                                                                    category?.name ??
                                                                        ''
                                                              });
                                                        },
                                                        textStyle: textTheme
                                                            .ralewayBold
                                                            .copyWith(
                                                          fontSize: 14,
                                                          color: Colors.white,
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                  const Gap(32),
                                                  Container(
                                                    width: size.width,
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 22),
                                                    decoration:
                                                        const BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.only(
                                                        topLeft:
                                                            Radius.circular(30),
                                                        topRight:
                                                            Radius.circular(30),
                                                      ),
                                                      color: AppColors.lightRed,
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Gap(24),
                                                        Text(
                                                          "Recommended workouts",
                                                          style: textTheme
                                                              .ralewaySemiBold
                                                              .copyWith(
                                                            fontSize: 17,
                                                            color:
                                                                AppColors.navy,
                                                          ),
                                                        ),
                                                        const Gap(16),
                                                        workouts?.isNotEmpty ==
                                                                true
                                                            ? ListView
                                                                .separated(
                                                                itemCount: workouts
                                                                        ?.length ??
                                                                    0,
                                                                shrinkWrap:
                                                                    true,
                                                                physics:
                                                                    const NeverScrollableScrollPhysics(),
                                                                itemBuilder:
                                                                    (BuildContext
                                                                            context,
                                                                        int index) {
                                                                  var item =
                                                                      workouts?[
                                                                          index][0];

                                                                  return ExerciseCard(
                                                                    title: '',
                                                                    subtitle:
                                                                        item?.seriesTitle ??
                                                                            'N/A',
                                                                    duration:
                                                                        item?.seriesDuration ??
                                                                            'N/A',
                                                                    tag: ((item !=
                                                                                null) &&
                                                                            (item.categories?.isNotEmpty ??
                                                                                false))
                                                                        ? item
                                                                            .categories![0]
                                                                        : 'N/A',
                                                                    thumbnail:
                                                                        item?.seriesImageUrl ??
                                                                            'N/A',
                                                                    onTap: () {
                                                                      context
                                                                          .pushRoute(
                                                                        WorkoutRoute(
                                                                          seriesId:
                                                                              item?.seriesId ?? '',
                                                                        ),
                                                                      );
                                                                      // mixpanel
                                                                      sl<MixpanelService>().trackButtonClick(
                                                                          'Workout Tapped',
                                                                          properties: {
                                                                            'Page':
                                                                                'Explore Page',
                                                                            'Code':
                                                                                'click.explore_page.workout_tapped',
                                                                            'Workout Name':
                                                                                item?.seriesTitle ?? ''
                                                                          });
                                                                    },
                                                                    isWorkout:
                                                                        true,
                                                                  );
                                                                },
                                                                separatorBuilder:
                                                                    (context,
                                                                        index) {
                                                                  return const Gap(
                                                                      12);
                                                                },
                                                              )
                                                            : SizedBox(
                                                                height: 100,
                                                                child: Center(
                                                                  child: NoData(
                                                                    textTheme:
                                                                        textTheme,
                                                                  ),
                                                                ),
                                                              ),
                                                        const Gap(144),
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Container(
                                        decoration: const BoxDecoration(
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(30),
                                            topRight: Radius.circular(30),
                                          ),
                                          color: AppColors.grey,
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Gap(28),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 0),
                                              child: SizedBox(
                                                height: 40,
                                                width: double.infinity,
                                                child: ListView.builder(
                                                  // controller: scrollController,
                                                  key: const Key(
                                                      "searchResults"),
                                                  itemCount: _menus.length + 2,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemBuilder:
                                                      (context, index) {
                                                    if (index == 0) {
                                                      return const Gap(24);
                                                    }
                                                    if (index ==
                                                        _menus.length + 1) {
                                                      return const Gap(24);
                                                    }
                                                    final menuIndex = index - 1;

                                                    return MenuButton(
                                                      text: _menus[menuIndex]
                                                          .name,
                                                      isSelected:
                                                          _menus[menuIndex]
                                                              .checked,
                                                      onTap: () {
                                                        _updateSelectedItem(
                                                            menuIndex);
                                                        if (menuIndex == 0) {
                                                          _params.isAll = true;
                                                          _params.isCompleted =
                                                              false;
                                                          _params.isWorkout =
                                                              false;
                                                          _params.isWorkout =
                                                              false;
                                                          _params.isExercise =
                                                              false;
                                                        } else if (menuIndex ==
                                                            1) {
                                                          _params.isAll = false;
                                                          _params.isCompleted =
                                                              false;
                                                          _params.isWorkout =
                                                              true;
                                                          _params.isExercise =
                                                              false;
                                                        } else if (menuIndex ==
                                                            2) {
                                                          _params.isAll = false;
                                                          _params.isCompleted =
                                                              false;
                                                          _params.isWorkout =
                                                              false;
                                                          _params.isExercise =
                                                              true;
                                                          _params.isWorkout =
                                                              false;
                                                        } else if (menuIndex ==
                                                            3) {
                                                          _params.isAll = false;
                                                          _params.isCompleted =
                                                              true;
                                                          _params.isWorkout =
                                                              false;
                                                          _params.isWorkout =
                                                              false;
                                                        }
                                                        context
                                                            .read<
                                                                ExploreCubit>()
                                                            .getFilteredExercises(
                                                              _params,
                                                            );
                                                        // mixpanel
                                                        sl<MixpanelService>()
                                                            .trackButtonClick(
                                                                'Menu Button Tapped',
                                                                properties: {
                                                              'Page':
                                                                  'Explore Page',
                                                              'Code':
                                                                  'click.explore_page.menu_button_tapped',
                                                              'Menu Name': _menus[
                                                                      menuIndex]
                                                                  .name
                                                            });
                                                      },
                                                      textStyle: textTheme
                                                          .ralewaySemiBold
                                                          .copyWith(
                                                              fontSize: 14),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 24,
                                                vertical: 12,
                                              ),
                                              child: Text(
                                                '${filteredList.length} Items',
                                                style: textTheme.ralewayRegular
                                                    .copyWith(
                                                  fontSize: 14,
                                                  color: AppColors.navy,
                                                ),
                                              ),
                                            ),
                                            Expanded(
                                              child: filteredList.isEmpty
                                                  ? Column(
                                                      children: [
                                                        const Spacer(),
                                                        ExploreNoData(
                                                            textTheme:
                                                                textTheme,
                                                            val: _searchController
                                                                .text
                                                                .toString()),
                                                        const Gap(60),
                                                        const Spacer()
                                                      ],
                                                    )
                                                  : ListView.separated(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        top: 4,
                                                        bottom: 144,
                                                        left: 24,
                                                        right: 24,
                                                      ),
                                                      itemCount:
                                                          filteredList.length,
                                                      shrinkWrap: true,
                                                      scrollDirection:
                                                          Axis.vertical,
                                                      itemBuilder:
                                                          (context, index) {
                                                        var item =
                                                            filteredList[index];
                                                        return ExerciseResult(
                                                          isExercise:
                                                              item.isExercise,
                                                          id: item.id,
                                                          title: item.title,
                                                          subtitle:
                                                              item.subtitle,
                                                          duration:
                                                              item.duration,
                                                          imageUrl:
                                                              item.imageUrl,
                                                          url: item.url,
                                                          onTap: () {
                                                            if (item.isExercise ==
                                                                true) {
                                                              Navigator.of(
                                                                      context)
                                                                  .push(
                                                                MaterialPageRoute(
                                                                  builder:
                                                                      (context) =>
                                                                          MixedExercisePage(
                                                                    notification:
                                                                        false,
                                                                    id: item?.id ??
                                                                        '',
                                                                    seriesId:
                                                                        "",
                                                                    isLast:
                                                                        false,
                                                                    isFirst:
                                                                        false,
                                                                    isOverride:
                                                                        false,
                                                                  ),
                                                                ),
                                                              );
                                                              // mixpanel
                                                              sl<MixpanelService>()
                                                                  .trackButtonClick(
                                                                      'Exercise Tapped',
                                                                      properties: {
                                                                    'Page':
                                                                        'Explore Page',
                                                                    'Code':
                                                                        'click.explore_page.exercise_tapped',
                                                                    'Exercise Name':
                                                                        item.title,
                                                                  });
                                                              return;
                                                            } else {
                                                              context.pushRoute(
                                                                WorkoutRoute(
                                                                    seriesId:
                                                                        item.id ??
                                                                            ''),
                                                              );
                                                              // mixpanel
                                                              sl<MixpanelService>()
                                                                  .trackButtonClick(
                                                                      'Workout Tapped',
                                                                      properties: {
                                                                    'Page':
                                                                        'Explore Page',
                                                                    'Code':
                                                                        'click.explore_page.workout_tapped',
                                                                    'Workout Name':
                                                                        item.title
                                                                  });
                                                              return;
                                                            }
                                                          },
                                                          textTheme: textTheme,
                                                          isFavourite:
                                                              item.isFavorite,
                                                        );
                                                      },
                                                      separatorBuilder:
                                                          (BuildContext context,
                                                              int index) {
                                                        return const Gap(16);
                                                      },
                                                    ),
                                            ),
                                          ],
                                        ),
                                      ),
                              ),
                            ],
                          ),
                          // Search Results Overlay
                          if (_isSearching)
                            Builder(
                              key: const Key("searchOverlay"),
                              builder: (context) {
                                // Correctly get the RenderBox of the search bar
                                final RenderBox? searchBarRenderBox =
                                    _searchBarKey.currentContext
                                        ?.findRenderObject() as RenderBox?;
                                // Get the size of the search bar
                                final searchBarSize = searchBarRenderBox?.size;
                                // Get the global position of the search bar
                                final searchBarPosition = searchBarRenderBox
                                    ?.localToGlobal(Offset.zero);

                                // Calculate the top position for the overlay
                                // It should be below the search bar
                                double overlayTop = 0;
                                if (searchBarPosition != null &&
                                    searchBarSize != null) {
                                  overlayTop = searchBarSize.height - 12;
                                }
                                return Positioned(
                                  top: overlayTop,
                                  left: 24,
                                  right: 24,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: AppColors.midBlue,
                                      ),
                                    ),
                                    constraints: BoxConstraints(
                                      maxHeight: (searchSuggestions.isNotEmpty)
                                          ? 240
                                          : 80,
                                    ),
                                    child: searchSuggestions.isNotEmpty
                                        ? Scrollbar(
                                            controller:
                                                _searchSuggestionsScrollController, // Add controller here
                                            thumbVisibility: true,
                                            trackVisibility: true,
                                            child: ListView.separated(
                                              controller:
                                                  _searchSuggestionsScrollController, // Add controller here
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                vertical: 16,
                                                horizontal: 8,
                                              ),
                                              shrinkWrap: true,
                                              itemCount:
                                                  searchSuggestions.length > 5
                                                      ? 5
                                                      : searchSuggestions
                                                          .length,
                                              separatorBuilder:
                                                  (context, index) {
                                                return const Gap(12);
                                              },
                                              itemBuilder: (context, index) {
                                                final result =
                                                    searchSuggestions[index];
                                                return ListTile(
                                                  leading: Container(
                                                    clipBehavior: Clip.hardEdge,
                                                    height: 100,
                                                    width: 80,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              6),
                                                    ),
                                                    child:
                                                        NetworkImageWithIndicator(
                                                      imageUrl: result.imageUrl
                                                          .toString()
                                                          .exerciseUrl,
                                                    ),
                                                  ),
                                                  title: Text(
                                                    result.title,
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: textTheme.ralewayBold
                                                        .copyWith(
                                                      fontSize: 12,
                                                      color: AppColors.navy,
                                                    ),
                                                  ),
                                                  subtitle: Text(
                                                    '${result.duration} . ${result.subtitle}',
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: textTheme
                                                        .ralewayRegular
                                                        .copyWith(
                                                      fontSize: 10,
                                                      color: AppColors.navy,
                                                    ),
                                                  ),
                                                  onTap: () {
                                                    if (result.isExercise ==
                                                        true) {
                                                      Navigator.of(context)
                                                          .push(
                                                        MaterialPageRoute(
                                                          builder: (context) =>
                                                              MixedExercisePage(
                                                            notification: false,
                                                            id: result?.id ??
                                                                '',
                                                            seriesId: "",
                                                            isLast: false,
                                                            isFirst: false,
                                                            isOverride: false,
                                                          ),
                                                        ),
                                                      );
                                                      // mixpanel
                                                      sl<MixpanelService>()
                                                          .trackButtonClick(
                                                              'Exercise Tapped',
                                                              properties: {
                                                            'Page':
                                                                'Explore Page',
                                                            'Code':
                                                                'click.explore_page.exercise_tapped',
                                                            'Exercise Name':
                                                                result.title,
                                                          });
                                                      return;
                                                    } else {
                                                      context.pushRoute(
                                                        WorkoutRoute(
                                                            seriesId:
                                                                result.id ??
                                                                    ''),
                                                      );
                                                      // mixpanel
                                                      sl<MixpanelService>()
                                                          .trackButtonClick(
                                                              'Workout Tapped',
                                                              properties: {
                                                            'Page':
                                                                'Explore Page',
                                                            'Code':
                                                                'click.explore_page.workout_tapped',
                                                            'Workout Name':
                                                                result.title
                                                          });
                                                      return;
                                                    }
                                                  },
                                                );
                                              },
                                            ),
                                          )
                                        : Center(
                                            child: Text(
                                              _searchController.text.length <= 2
                                                  ? 'Searching...'
                                                  : 'No results found',
                                              style: textTheme.labelsBold,
                                            ),
                                          ),
                                  ),
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }
}

class Menu {
  final String name;
  bool checked;
  int selectedVenue;
  Menu({
    required this.name,
    required this.checked,
    required this.selectedVenue,
  });
}

class CustomSearchBar extends StatefulWidget {
  final double width;
  final Function(String) onSearchChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextEditingController? controller;
  final bool isCategoriesShown;

  const CustomSearchBar({
    super.key,
    required this.width,
    required this.onSearchChanged,
    this.onSubmitted,
    this.onClear,
    this.textStyle,
    this.hintStyle,
    this.controller,
    this.isCategoriesShown = false,
  });

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  late final TextEditingController _searchController;
  String _lastSubmitted = '';

  @override
  void initState() {
    super.initState();
    _searchController = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _searchController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.midBlue, width: 1.5),
        borderRadius: BorderRadius.circular(30),
      ),
      child: TextField(
        style: widget.textStyle ??
            const TextStyle(
              fontSize: 12,
              color: AppColors.navy,
            ),
        controller: _searchController,
        onChanged: (text) {
          EasyDebounce.debounce(
            'my-debouncer',
            const Duration(milliseconds: 1000),
            () {
              // Only call onSearchChanged if not the same as last submitted text
              if (text != _lastSubmitted) {
                widget.onSearchChanged(text);
              }
            },
          );
        },
        onSubmitted: (text) {
          // Cancel any pending debounced calls
          EasyDebounce.cancel('my-debouncer');

          // Remember the submitted text
          _lastSubmitted = text;

          // Call onSubmitted if provided
          if (widget.onSubmitted != null) {
            widget.onSubmitted!(text);
          }
        },
        decoration: InputDecoration(
          hintText: 'What are you looking for...',
          prefixIcon: const Padding(
            padding: EdgeInsets.only(left: 12),
            child: Icon(
              Icons.search,
              color: AppColors.navy,
            ),
          ),
          hintStyle: widget.hintStyle ??
              const TextStyle(
                fontSize: 12,
                color: AppColors.navy,
              ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.close, color: Colors.grey),
                  onPressed: () {
                    setState(() {
                      _searchController.clear();
                      _lastSubmitted = '';
                      widget.onClear?.call();
                    });
                  },
                )
              : null,
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
        ),
        readOnly: false,
      ),
    );
  }
}

class ExerciseResult extends StatelessWidget {
  final String title;
  final String id;
  final String subtitle;
  final String duration;
  final String imageUrl;
  final VoidCallback onTap;
  final TextTheme textTheme;
  final bool isFavourite;
  final bool isExercise;
  final String url;

  const ExerciseResult({
    super.key,
    required this.title,
    required this.subtitle,
    required this.duration,
    required this.imageUrl,
    required this.onTap,
    required this.textTheme,
    required this.id,
    required this.isFavourite,
    required this.isExercise,
    required this.url,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            clipBehavior: Clip.hardEdge,
            height: 80,
            width: 104,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: NetworkImageWithIndicator(
              imageUrl: isExercise ? imageUrl.exerciseUrl : imageUrl.workoutUrl,
            ),
          ),
          const Gap(12),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: isIos ? 4 : 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: textTheme.gothamBold.copyWith(
                      fontSize: 14,
                      color: AppColors.navy,
                    ),
                  ),
                  Text(
                    subtitle,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: textTheme.ralewayRegular.copyWith(
                      fontSize: 14,
                      color: AppColors.navy,
                    ),
                  ),
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time_rounded,
                        size: 15,
                      ),
                      const Gap(5),
                      Flexible(
                        child: Text(
                          duration,
                          overflow: TextOverflow.ellipsis,
                          style: textTheme.ralewayRegular.copyWith(
                            fontSize: 14,
                            color: AppColors.navy,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          buildPopupMenuButton(
            context: context,
            id: id,
            isFavourite: isFavourite,
            isExercise: isExercise,
            url: url,
          ),
        ],
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  final String imageUrl;
  final String categoryName;
  final VoidCallback onTap;
  final TextStyle? textStyle;
  final double borderRadius;
  final Color overlayColor;

  const CategoryCard({
    super.key,
    required this.imageUrl,
    required this.categoryName,
    required this.onTap,
    this.textStyle,
    this.borderRadius = 16,
    this.overlayColor = Colors.black26,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Background Image with NetworkImage and a loading indicator
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: NetworkImageWithIndicator(
              imageUrl: imageUrl.categoryUrl,
            ),
          ),

          // Overlay
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              color: overlayColor,
            ),
          ),

          // Text Content
          Container(
            padding: const EdgeInsets.all(16),
            alignment: Alignment.center,
            child: Text(
              categoryName,
              style: textStyle ??
                  const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class MenuButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final Color selectedTextColor;
  final Color unselectedTextColor;
  final TextStyle? textStyle;
  final double height;
  final double horizontalPadding;

  const MenuButton({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
    this.selectedColor = Colors.red,
    this.unselectedColor = Colors.white,
    this.selectedTextColor = Colors.white,
    this.unselectedTextColor = Colors.black,
    this.textStyle,
    this.height = 55,
    this.horizontalPadding = 15,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 0, 8, 0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: height,
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? selectedColor : Colors.transparent,
            ),
            color: isSelected ? selectedColor : unselectedColor,
            borderRadius: const BorderRadius.all(Radius.circular(100)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                child: Text(
                  text,
                  style: (textStyle ?? const TextStyle()).copyWith(
                    color: isSelected ? selectedTextColor : unselectedTextColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
